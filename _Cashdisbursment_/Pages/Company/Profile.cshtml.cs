using _Cashdisbursment_.Models;
using _Cashdisbursment_.Services;
using _Cashdisbursment_.Utilities;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.RazorPages;
using System.ComponentModel.DataAnnotations;

namespace _Cashdisbursment_.Pages.Company
{
    public class ProfileModel : PageModel
    {
        private readonly CompanyService _companyService;
        private readonly ApplicationService _applicationService;

        public ProfileModel(CompanyService companyService, ApplicationService applicationService)
        {
            _companyService = companyService;
            _applicationService = applicationService;
        }

        public User? CurrentUser { get; set; }
        public Models.Company? Company { get; set; }
        public int TotalApplications { get; set; }
        public int ApprovedApplications { get; set; }

        [BindProperty]
        [Required]
        [StringLength(255)]
        public string Name { get; set; } = string.Empty;

        [BindProperty]
        [Required]
        [EmailAddress]
        [StringLength(255)]
        public string Email { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(15)]
        public string Contact { get; set; } = string.Empty;

        [BindProperty]
        [StringLength(500)]
        public string Address { get; set; } = string.Empty;

        [TempData]
        public string? SuccessMessage { get; set; }

        [TempData]
        public string? ErrorMessage { get; set; }

        public async Task<IActionResult> OnGetAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsCompanyUser(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            if (CurrentUser.CompanyID.HasValue)
            {
                Company = await _companyService.GetCompanyByIdAsync(CurrentUser.CompanyID.Value);
                
                if (Company != null)
                {
                    // Populate form fields
                    Name = Company.Name;
                    Email = Company.Email;
                    Contact = Company.Contact;
                    Address = Company.Address;

                    // Get statistics
                    var applications = await _applicationService.GetApplicationsByCompanyAsync(CurrentUser.CompanyID.Value);
                    TotalApplications = applications.Count;
                    ApprovedApplications = applications.Count(a => a.Status == "Approved");
                }
            }

            return Page();
        }

        public async Task<IActionResult> OnPostAsync()
        {
            CurrentUser = AuthUtility.GetCurrentUser(HttpContext.Session);
            
            if (CurrentUser == null || !AuthUtility.IsCompanyUser(HttpContext.Session))
            {
                return RedirectToPage("/Account/Login");
            }

            if (!ModelState.IsValid)
            {
                // Reload company data for display
                if (CurrentUser.CompanyID.HasValue)
                {
                    Company = await _companyService.GetCompanyByIdAsync(CurrentUser.CompanyID.Value);
                    
                    // Get statistics
                    var applications = await _applicationService.GetApplicationsByCompanyAsync(CurrentUser.CompanyID.Value);
                    TotalApplications = applications.Count;
                    ApprovedApplications = applications.Count(a => a.Status);
                }
                return Page();
            }

            try
            {
                if (CurrentUser.CompanyID.HasValue)
                {
                    // Check if email is already taken by another company
                    if (await _companyService.EmailExistsAsync(Email, CurrentUser.CompanyID.Value))
                    {
                        ErrorMessage = "This email is already in use by another company.";
                        return await OnGetAsync();
                    }

                    // Update company
                    var company = new Models.Company
                    {
                        CompanyID = CurrentUser.CompanyID.Value,
                        Name = Name,
                        Email = Email,
                        Contact = Contact,
                        Address = Address,
                        Status = true // Keep existing status
                    };

                    var updated = await _companyService.UpdateCompanyAsync(company);

                    if (updated)
                    {
                        SuccessMessage = "Profile updated successfully!";
                        return RedirectToPage();
                    }
                    else
                    {
                        ErrorMessage = "Failed to update profile. Please try again.";
                        return await OnGetAsync();
                    }
                }
                else
                {
                    ErrorMessage = "Company information not found.";
                    return await OnGetAsync();
                }
            }
            catch (Exception ex)
            {
                ErrorMessage = "An error occurred while updating the profile. Please try again.";
                // Log the exception in a real application
                return await OnGetAsync();
            }
        }
    }
}
